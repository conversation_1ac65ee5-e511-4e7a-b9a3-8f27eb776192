import os, sys

# This is just a simple scirpt of hello world
#print("Hello World!")
#print("Hello World Again!")

def add(*number:float):
    result:float = 0.0
    for i in number:
        result += float(i)
    print(f"Result: {result}")

def minus(*number:float):
    result:float = 0.0
    for i in number:
        result -= float(i)
    print(f"Result: {result}")

def multiply(*number:float):
    result:float = 1.0
    for i in number:
        result *= float(i)
    print(f"Result: {result}")

def divide(*number:float):
    result:float = 1.0
    for i in number:
        result /= float(i)
    print(f"Result: {result}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python hello_world.py <num1> <num2> <num3>")
    #add(sys.argv[1], sys.argv[2], sys.argv[3])
    minus(sys.argv[1], sys.argv[2])
    #multiply(sys.argv[1], sys.argv[2])
    #divide(sys.argv[1], sys.argv[2])
